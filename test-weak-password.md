# 弱密码强制修改功能测试指南

## 功能概述
当用户使用正确但强度不足的密码登录时，系统会检测到弱密码并强制用户修改为更安全的密码。

## 测试步骤

### 1. 准备测试用户
确保系统中有一个使用弱密码的测试用户，例如：
- 用户名：testuser
- 密码：123456 (这是一个典型的弱密码)

### 2. 测试弱密码检测
1. 访问登录页面
2. 输入用户名和弱密码
3. 点击登录按钮
4. 系统应该：
   - 验证密码正确性
   - 检测到密码强度不足
   - 弹出强制修改密码对话框

### 3. 测试密码修改
1. 在弹出的对话框中：
   - 用户名字段应该自动填充且不可编辑
   - 原密码字段应该自动填充
   - 输入新密码（必须符合强度要求）
   - 确认新密码
2. 点击"修改密码"按钮
3. 系统应该：
   - 验证新密码强度
   - 更新用户密码
   - 显示成功消息
   - 关闭对话框

### 4. 验证修改结果
1. 使用新密码重新登录
2. 应该能够正常登录，不再弹出强制修改对话框

## 常见弱密码示例
- 123456
- password
- admin
- 12345678
- qwerty
- abc123

## 密码强度要求
- 长度：8-32个字符
- 复杂度：至少包含数字、小写字母、大写字母、特殊字符中的三种
- 不能包含用户名
- 不能使用常见弱密码

## 技术实现要点

### 后端
1. `MyAuthenticationProvider` - 在密码验证成功后检测弱密码
2. `WeakPasswordException` - 专门的弱密码异常类
3. `forceResetWeakPassword` API - 强制修改密码接口
4. HTTP状态码605 - 弱密码响应码

### 前端
1. 登录错误处理 - 检测605响应码
2. 弱密码修改对话框 - 在登录页面内嵌
3. 密码强度验证 - 前端表单验证
4. 用户体验优化 - 自动填充用户名和原密码

## 安全特性
- 只有输入正确密码的用户才能进入强制修改流程
- 验证原密码确实为弱密码
- 新密码必须通过强度检测
- 修改成功后需要重新登录

## 故障排除

### 问题1：登录时没有弹出修改密码对话框
- 检查用户密码是否确实为弱密码
- 检查后端弱密码检测逻辑是否正常工作
- 检查前端错误处理逻辑

### 问题2：修改密码失败
- 检查新密码是否符合强度要求
- 检查API接口是否正常工作
- 检查网络连接

### 问题3：修改成功后仍然弹出对话框
- 检查密码是否真正更新到数据库
- 清除浏览器缓存重试
