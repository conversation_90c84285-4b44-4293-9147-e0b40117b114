import axios from 'axios'
import { Notification, MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { tansParams, blobValidate } from "@/utils/ruoyi";

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // baseURL: 'https://bulkbarge.gzport.com/gzgapp',
  // 超时
  timeout: 100000
})
// request拦截器
service.interceptors.request.use(
  config => {
    if (getToken()) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    return config
  },
  error => {
    console.log(error)
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(res => {
    const code = res.data.code
    if (code === 401) {
      MessageBox.confirm(
        '登录状态已过期，您可以继续留在该页面，或者重新登录',
        '系统提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        store.dispatch('LogOut').then(() => {
          // location.reload() // 为了重新实例化vue-router对象 避免bug
          location = '/gs/index'
        })
      })
    } else if (code !== 200) {
      // {{ AURA-X: Add - 特殊处理弱密码响应码，不显示错误通知，直接传递错误信息. Approval: 寸止(ID:1722330009). }}
      if (code === 605) {
        // 弱密码响应码，直接传递错误信息给前端处理
        return Promise.reject(res.data)
      }
      Notification.error({
        title: res.data.msg
      })
      return Promise.reject('error')
    } else {
      return res.data
    }
  },
  error => {
    console.log('err' + error)
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)


// 通用下载方法
export function download(url, params, filename, config) {
  // downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
      transformRequest: [(params) => { return tansParams(params) }],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config
  }).then(async(data) => {
    console.log(1)
      const isBlob = blobValidate(data);
      if (isBlob) {
        console.log(11)
          const blob = new Blob([data])
          saveAs(blob, filename)
      } else {
          const resText = await data.text();
          const rspObj = JSON.parse(resText);
          const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
          Message.error(errMsg);
      }
      // downloadLoadingInstance.close();
  }).catch((r) => {
      console.error(r)
      Message.error('下载文件出现错误，请联系管理员！')
      // downloadLoadingInstance.close();
  })
}


export function downloadMinioto(fileId, fileName) {

  let params = {
      fileId: fileId,
      timeStamp: new Date().getTime()
  }

  let url = '/commonDownload/downloadMinioto'

  download(url, params, fileName)

}

export default service
