package com.ruoyi.web.controller.system;

/**
 * 弱密码重置请求类
 * 
 * <AUTHOR>
 */
public class WeakPasswordResetRequest
{
    /** 用户名 */
    private String username;
    
    /** 原密码 */
    private String oldPassword;
    
    /** 新密码 */
    private String newPassword;

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getOldPassword()
    {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword)
    {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword()
    {
        return newPassword;
    }

    public void setNewPassword(String newPassword)
    {
        this.newPassword = newPassword;
    }

    @Override
    public String toString()
    {
        return "WeakPasswordResetRequest{" +
                "username='" + username + '\'' +
                ", oldPassword='***'" +
                ", newPassword='***'" +
                '}';
    }
}
