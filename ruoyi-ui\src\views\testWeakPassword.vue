<template>
  <div class="test-weak-password">
    <el-card>
      <div slot="header">
        <span>弱密码检测测试</span>
      </div>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="用户名">
          <el-input v-model="testForm.username" placeholder="请输入测试用户名" />
        </el-form-item>
        
        <el-form-item label="弱密码">
          <el-input v-model="testForm.weakPassword" type="password" placeholder="请输入弱密码进行测试" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testWeakPasswordLogin">测试弱密码登录</el-button>
          <el-button @click="testPasswordStrength">测试密码强度</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider>测试结果</el-divider>
      
      <div v-if="testResult">
        <el-alert
          :title="testResult.title"
          :type="testResult.type"
          :description="testResult.description"
          show-icon
        />
      </div>
      
      <el-divider>常见弱密码示例</el-divider>
      
      <el-table :data="weakPasswordExamples" border>
        <el-table-column prop="password" label="密码" width="150" />
        <el-table-column prop="reason" label="弱密码原因" />
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" @click="useExample(scope.row.password)">使用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { login } from "@/api/login";

export default {
  name: "TestWeakPassword",
  data() {
    return {
      testForm: {
        username: 'admin',
        weakPassword: '123456'
      },
      testResult: null,
      weakPasswordExamples: [
        { password: '123456', reason: '纯数字，过于简单' },
        { password: 'password', reason: '常见弱密码' },
        { password: 'admin', reason: '与用户名相同' },
        { password: '12345678', reason: '连续数字' },
        { password: 'qwerty', reason: '键盘序列' },
        { password: 'abc123', reason: '字母数字组合但过于简单' }
      ]
    };
  },
  methods: {
    testWeakPasswordLogin() {
      this.testResult = null;
      
      login(this.testForm.username, this.testForm.weakPassword, '', '').then(res => {
        this.testResult = {
          title: '登录成功',
          type: 'success',
          description: '密码强度足够，登录成功'
        };
      }).catch(error => {
        console.log('Test login error:', error);
        
        if (error && error.code === 605) {
          this.testResult = {
            title: '检测到弱密码',
            type: 'warning',
            description: `弱密码检测成功！错误信息：${error.msg}`
          };
        } else {
          this.testResult = {
            title: '登录失败',
            type: 'error',
            description: error.msg || '登录失败，可能是用户名或密码错误'
          };
        }
      });
    },
    
    testPasswordStrength() {
      // 这里可以添加直接调用密码强度检测API的逻辑
      this.$message.info('密码强度检测功能需要后端API支持');
    },
    
    useExample(password) {
      this.testForm.weakPassword = password;
    }
  }
};
</script>

<style scoped>
.test-weak-password {
  padding: 20px;
}
</style>
