<template>
  <div class="force-reset-password">
    <div class="reset-container">
      <div class="reset-header">
        <h2>密码强度不足，请立即修改</h2>
        <p>为了您的账户安全，检测到您的密码强度不足，请立即修改为更安全的密码。</p>
      </div>
      
      <el-form
        ref="resetForm"
        :model="resetForm"
        :rules="resetRules"
        class="reset-form"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="resetForm.username"
            placeholder="请输入用户名"
            :disabled="true"
          />
        </el-form-item>
        
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="resetForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleResetPassword"
            style="width: 100%"
          >
            修改密码
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="password-tips">
        <h4>密码要求：</h4>
        <ul>
          <li>长度至少8位，最多32位</li>
          <li>至少包含数字、小写字母、大写字母、特殊字符中的三种</li>
          <li>不能包含用户名</li>
          <li>不能使用常见弱密码</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { forceResetWeakPassword } from "@/api/system/user";

export default {
  name: "ForceResetPassword",
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetForm.newPassword) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    
    return {
      loading: false,
      resetForm: {
        username: "",
        oldPassword: "",
        newPassword: "",
        confirmPassword: ""
      },
      resetRules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" }
        ],
        oldPassword: [
          { required: true, message: "原密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 8, max: 32, message: "密码长度必须在8到32个字符之间", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: validateConfirmPassword, trigger: "blur" }
        ]
      }
    };
  },
  created() {
    // 从路由参数或localStorage获取用户名
    this.resetForm.username = this.$route.query.username || localStorage.getItem('weakPasswordUsername') || '';
  },
  methods: {
    handleResetPassword() {
      this.$refs.resetForm.validate(valid => {
        if (valid) {
          this.loading = true;
          forceResetWeakPassword(
            this.resetForm.username,
            this.resetForm.oldPassword,
            this.resetForm.newPassword
          ).then(response => {
            this.$message.success("密码修改成功，请重新登录");
            // 清除相关缓存
            localStorage.removeItem('weakPasswordUsername');
            // 跳转到登录页
            this.$router.push('/login');
          }).catch(error => {
            this.$message.error(error.msg || "密码修改失败");
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.force-reset-password {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.reset-container {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 500px;
}

.reset-header {
  text-align: center;
  margin-bottom: 30px;
}

.reset-header h2 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.reset-header p {
  color: #666;
  font-size: 14px;
}

.reset-form {
  margin-bottom: 20px;
}

.password-tips {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border-left: 4px solid #007bff;
}

.password-tips h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.password-tips li {
  color: #666;
  font-size: 13px;
  margin-bottom: 5px;
}
</style>
