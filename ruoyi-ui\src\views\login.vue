<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">广州港船务水上过驳服务平台</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item> -->
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright &copy; {{getYear}} 广州港数据科技有限公司 All Rights Reserved</span>
    </div>

    <!-- 弱密码修改对话框 -->
    <el-dialog
      title="密码强度不足，请立即修改"
      :visible.sync="showWeakPasswordDialog"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="weak-password-notice">
        <el-alert
          title="安全提醒"
          description="检测到您的密码强度不足，为了账户安全，请立即修改为更安全的密码。"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>

      <el-form
        ref="weakPasswordForm"
        :model="weakPasswordForm"
        :rules="weakPasswordRules"
        label-width="100px"
        style="margin-top: 20px;"
      >
        <el-form-item label="用户名">
          <el-input v-model="weakPasswordForm.username" :disabled="true" />
        </el-form-item>

        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="weakPasswordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="weakPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="weakPasswordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <div class="password-tips">
        <h4>密码要求：</h4>
        <ul>
          <li>长度至少8位，最多32位</li>
          <li>至少包含数字、小写字母、大写字母、特殊字符中的三种</li>
          <li>不能包含用户名</li>
          <li>不能使用常见弱密码</li>
        </ul>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="resetLoading" @click="handleWeakPasswordReset">
          修改密码
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import { forceResetWeakPassword } from "@/api/system/user";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        // code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" }
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" }
        ],
        // code: [{ required: true, trigger: "change", message: "验证码不能为空" }]
      },
      loading: false,
      redirect: undefined,
      // {{ AURA-X: Add - 弱密码修改对话框相关数据. Approval: 寸止(ID:1722330013). }}
      showWeakPasswordDialog: false,
      resetLoading: false,
      weakPasswordForm: {
        username: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      weakPasswordRules: {
        oldPassword: [
          { required: true, message: "原密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 8, max: 32, message: "密码长度必须在8到32个字符之间", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: this.validateConfirmPassword, trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  computed: {
    getYear(){
      return new Date().getFullYear()
    }
  },
  created() {
    // this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              const userName = localStorage.getItem('userName')
              if(userName === 'xsadmin') {
                this.$router.push({
                  name: 'Port'
                })
              } else {
                this.$router.push({ path: this.redirect || "/" });
              }
            })
            .catch((error) => {
              this.loading = false;
              // {{ AURA-X: Modify - 增强弱密码错误处理逻辑. Approval: 寸止(ID:1722330011). }}
              console.log('Login error:', error); // 调试日志

              // 检查是否为弱密码错误
              if (error && error.code === 605) {
                // 弱密码，显示修改密码对话框
                this.weakPasswordForm.username = this.loginForm.username;
                this.weakPasswordForm.oldPassword = this.loginForm.password;
                this.showWeakPasswordDialog = true;
                return;
              }

              // 其他错误情况
              this.getCode();
            });
        }
      });
    },

    // {{ AURA-X: Add - 处理弱密码修改的方法. Approval: 寸止(ID:1722330014). }}
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.weakPasswordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },

    handleWeakPasswordReset() {
      this.$refs.weakPasswordForm.validate(valid => {
        if (valid) {
          this.resetLoading = true;
          forceResetWeakPassword(
            this.weakPasswordForm.username,
            this.weakPasswordForm.oldPassword,
            this.weakPasswordForm.newPassword
          ).then(response => {
            this.$message.success('密码修改成功，请重新登录');
            this.showWeakPasswordDialog = false;
            this.resetWeakPasswordForm();
          }).catch(error => {
            this.$message.error(error.msg || '密码修改失败');
          }).finally(() => {
            this.resetLoading = false;
          });
        }
      });
    },

    resetWeakPasswordForm() {
      this.weakPasswordForm = {
        username: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      if (this.$refs.weakPasswordForm) {
        this.$refs.weakPasswordForm.resetFields();
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/image/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
