<!DOCTYPE html>
<html>
<head>
    <title>测试弱密码修改API</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>弱密码修改API测试</h1>
    
    <div>
        <h3>测试强制修改弱密码接口</h3>
        <form id="testForm">
            <div>
                <label>用户名:</label>
                <input type="text" id="username" value="admin" />
            </div>
            <div>
                <label>原密码:</label>
                <input type="password" id="oldPassword" value="123456" />
            </div>
            <div>
                <label>新密码:</label>
                <input type="password" id="newPassword" value="Admin123!@#" />
            </div>
            <button type="button" onclick="testAPI()">测试API</button>
        </form>
    </div>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;">
        <h4>测试结果:</h4>
        <pre id="resultContent">等待测试...</pre>
    </div>

    <script>
        function testAPI() {
            const username = document.getElementById('username').value;
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            
            const data = {
                username: username,
                oldPassword: oldPassword,
                newPassword: newPassword
            };
            
            console.log('发送请求:', data);
            
            axios.post('/system/user/forceResetWeakPassword', data)
                .then(response => {
                    console.log('成功响应:', response);
                    document.getElementById('resultContent').textContent = 
                        '成功!\n' + JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.log('错误响应:', error);
                    document.getElementById('resultContent').textContent = 
                        '错误!\n' + JSON.stringify(error.response ? error.response.data : error.message, null, 2);
                });
        }
    </script>
</body>
</html>
