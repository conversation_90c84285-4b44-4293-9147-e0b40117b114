package com.ruoyi.notice.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.notice.bo.MiniappStatementBO;
import com.ruoyi.notice.service.MiniappStatementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 告知书和声明表(MiniappStatement)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-17 15:46:17
 */
@RestController
@RequestMapping("/notice")
@Slf4j
public class MiniappStatementController {
    /**
     * 服务对象
     */
    @Resource
    private MiniappStatementService miniappStatementService;

    /**
     * 分页查询
     *
     * @param miniappStatementBO 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @GetMapping
    public ResponseEntity<Page<MiniappStatementBO>> queryByPage(MiniappStatementBO miniappStatementBO, PageRequest pageRequest) {
        return ResponseEntity.ok(this.miniappStatementService.queryByPage(miniappStatementBO, pageRequest));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public ResponseEntity<MiniappStatementBO> queryById(@PathVariable("id") Integer id) {
        return ResponseEntity.ok(this.miniappStatementService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param miniappStatementBO 实体
     * @return 新增结果
     */
    @PostMapping
    public ResponseEntity<MiniappStatementBO> add(@RequestBody MiniappStatementBO miniappStatementBO) {
        return ResponseEntity.ok(this.miniappStatementService.insert(miniappStatementBO));
    }

    /**
     * 编辑数据
     *
     * @param miniappStatementBO 实体
     * @return 编辑结果
     */
    @PutMapping
    public ResponseEntity<MiniappStatementBO> edit(@RequestBody MiniappStatementBO miniappStatementBO) {
        return ResponseEntity.ok(this.miniappStatementService.update(miniappStatementBO));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public ResponseEntity<Boolean> deleteById(@RequestParam("id") Integer id) {
        return ResponseEntity.ok(this.miniappStatementService.deleteById(id));
    }
    /**
     * 保存船长声明信息
     *
     * @param MiniappStatementBO 货物运输详情对象，包含托运单明细id、海事处和泊位信息等
     * @return 返回保存结果，包括成功或错误信息
     */
    @PostMapping("/saveCaptainStatement")
    public AjaxResult saveCaptainStatement(@RequestBody MiniappStatementBO miniappStatementBO) {
        if (miniappStatementBO.getCargoconsignmentdetailId() == null) {
            log.error("托运单明细id不能为空");
            return AjaxResult.error("托运单明细id不能为空");
        }
//        if (StringUtils.isBlank(miniappStatementBO.getMaritimeOffice()) ||
//            StringUtils.isBlank(miniappStatementBO.getBerth())) {
//            log.error("海事处和泊位信息不能为空");
//            return AjaxResult.error("海事处和泊位信息不能为空");
//        }

        // 先查询是否存在
        MiniappStatementBO existingStatement = miniappStatementService.getStatementByConsignDetailId(
            miniappStatementBO.getCargoconsignmentdetailId());

        if (existingStatement != null) {
            // 存在则更新
            miniappStatementService.updateStatement(miniappStatementBO);
            return AjaxResult.success("船长声明已更新");
        } else {
            // 不存在则新增
            miniappStatementService.insertCaptainStatement(miniappStatementBO);
            return AjaxResult.success("船长声明已保存");
        }
    }

    /**
     * 获取船长声明
     */
    @GetMapping("/getCaptainStatement/{consignDetailId}")
    public AjaxResult getCaptainStatement(@PathVariable(required = false) Integer consignDetailId) {
        // 参数校验
        if (consignDetailId == null || consignDetailId.toString().equals("undefined")) {
            return AjaxResult.error("托运单明细ID不能为空");
        }

        MiniappStatementBO statement = miniappStatementService.getStatementByConsignDetailId(consignDetailId);
        return AjaxResult.success(statement);
    }

    /**
     * @param
     * @return
     * @description 根据水路运单id查询船长声明
     * <AUTHOR>
     * @date 2024/12/24 14:33
     */
    @GetMapping("/getCaptainStatementByWaterwayId/{waterwayId}")
    public AjaxResult getCaptainStatementByWaterwayId(@PathVariable Long waterwayId) {
        // 参数校验
        if (waterwayId == null || waterwayId.toString().equals("undefined")) {
            return AjaxResult.error("水路运单ID不能为空");
        }

        MiniappStatementBO statement = miniappStatementService.getStatementByWaterWayId(waterwayId);
        return AjaxResult.success(statement);
    }

    /**
     * 保存安全装货确认书
     */
    @PostMapping("/saveLoadingConfirm")
    public AjaxResult saveLoadingConfirm(@RequestBody MiniappStatementBO statement) {
        try {
            // 检查是否存在记录
            MiniappStatementBO existingStatement = miniappStatementService.getStatementByConsignDetailId(statement.getCargoconsignmentdetailId());
            
            if (existingStatement != null) {
                // 更新记录
                miniappStatementService.updateStatement(statement);
            } else {
                // 新增记录
                miniappStatementService.insertCaptainStatement(statement);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("保存安全装货确认书失败：" + e.getMessage());
        }
    }

    /**
     * 获取安全装货确认书
     */
    @GetMapping("/getLoadingConfirm/{consignDetailId}")
    public AjaxResult getLoadingConfirm(@PathVariable Integer consignDetailId) {
        try {
            MiniappStatementBO statement = miniappStatementService.getLoadingConfirm(consignDetailId);
            return AjaxResult.success(statement);
        } catch (Exception e) {
            return AjaxResult.error("获取安全装货确认书失败：" + e.getMessage());
        }
    }

}

