# 弱密码强制修改功能测试步骤

## 问题修复说明

已修复的问题：
1. **参数名解析错误** - 将`@RequestParam`改为`@RequestBody`，使用请求对象接收参数
2. **前端请求格式** - 将`params`改为`data`，发送JSON请求体
3. **用户体验优化** - 在登录页面内嵌修改密码对话框，避免页面跳转

## 测试准备

### 1. 确保系统中有测试用户
可以使用现有的admin用户，或创建一个新的测试用户：
- 用户名：testuser
- 密码：123456（这是一个弱密码）

### 2. 验证弱密码检测功能
确保PasswordValidationUtils.validatePassword()方法能正确识别弱密码。

## 测试步骤

### 步骤1：测试弱密码登录检测
1. 打开登录页面
2. 输入用户名和弱密码（如：admin/123456）
3. 点击登录
4. **预期结果**：应该弹出"密码强度不足，请立即修改"对话框

### 步骤2：测试密码修改功能
1. 在弹出的对话框中：
   - 用户名应该自动填充且不可编辑
   - 原密码应该自动填充
   - 输入新密码（如：Admin123!@#）
   - 确认新密码
2. 点击"修改密码"按钮
3. **预期结果**：显示"密码修改成功"消息，对话框关闭

### 步骤3：验证修改结果
1. 使用新密码重新登录
2. **预期结果**：能够正常登录，不再弹出强制修改对话框

## 调试信息

### 后端日志
在控制器中添加了调试日志：
```java
System.out.println("forceResetWeakPassword called with request: " + request);
```

### 前端调试
在浏览器开发者工具中查看：
1. Network标签页 - 检查API请求和响应
2. Console标签页 - 查看错误信息

### 常见问题排查

#### 问题1：对话框没有弹出
- 检查浏览器控制台是否有JavaScript错误
- 确认弱密码检测逻辑是否正常工作
- 检查前端错误处理代码

#### 问题2：API调用失败
- 检查网络请求是否正确发送
- 确认后端接口是否正常接收参数
- 查看后端日志输出

#### 问题3：密码修改失败
- 确认新密码是否符合强度要求
- 检查原密码验证逻辑
- 确认数据库更新是否成功

## 技术实现要点

### 后端修改
1. `WeakPasswordResetRequest` - 新的请求对象
2. `@RequestBody` - 接收JSON请求体
3. 参数验证和密码强度检测
4. 数据库更新逻辑

### 前端修改
1. 登录页面内嵌对话框
2. 错误处理逻辑（检测605响应码）
3. API调用使用`data`而不是`params`
4. 表单验证和用户体验优化

## 安全特性验证

1. **身份验证**：只有输入正确原密码的用户才能修改
2. **弱密码检测**：确认原密码确实为弱密码
3. **新密码强度**：新密码必须通过强度检测
4. **重新登录**：修改成功后需要重新登录验证

## 下一步

如果测试通过，可以考虑：
1. 移除调试日志
2. 添加更多的错误处理
3. 优化用户界面
4. 添加单元测试
