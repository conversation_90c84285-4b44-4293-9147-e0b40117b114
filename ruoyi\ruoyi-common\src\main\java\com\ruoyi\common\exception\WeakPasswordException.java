package com.ruoyi.common.exception;

/**
 * 弱密码异常
 * 用于标识用户使用了正确但强度不足的密码
 * 
 * <AUTHOR>
 */
public class WeakPasswordException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public WeakPasswordException(String message)
    {
        this.message = message;
    }

    public WeakPasswordException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }

    public WeakPasswordException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    public WeakPasswordException(String message, Throwable e, Integer code)
    {
        super(message, e);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage()
    {
        return message;
    }

    public Integer getCode()
    {
        return code;
    }
}
